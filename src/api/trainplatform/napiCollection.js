import request from '@/utils/request'

// 查询动态视频采集记录列表
export function listNapiCollection(query) {
  return request({
    url: '/trainplatform/napiCollection/list',
    method: 'get',
    params: query
  })
}

// 查询动态视频采集记录详细
export function getNapiCollection(id) {
  return request({
    url: '/trainplatform/napiCollection/' + id,
    method: 'get'
  })
}

// 新增动态视频采集记录
export function addNapiCollection(data) {
  return request({
    url: '/trainplatform/napiCollection',
    method: 'post',
    data: data
  })
}

// 修改动态视频采集记录
export function updateNapiCollection(data) {
  return request({
    url: '/trainplatform/napiCollection',
    method: 'put',
    data: data
  })
}

// 删除动态视频采集记录
export function delNapiCollection(id) {
  return request({
    url: '/trainplatform/napiCollection/' + id,
    method: 'delete'
  })
}
