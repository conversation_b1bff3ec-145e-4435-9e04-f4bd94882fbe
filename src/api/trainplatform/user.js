import request from '@/utils/request'

// 查询课题参与人员列表
export function listUser(query) {
  return request({
    url: '/trainplatform/project/user/list',
    method: 'post',
    data: query
  })
}

// 查询课题下的角色
export function getRoleByProject() {
  return request({
    url: '/trainplatform/project/role/enum',
    method: 'get'
  })
}

// 查询课题参与人员详细
export function getUser(id) {
  return request({
    url: '/trainplatform/project/user/' + id,
    method: 'get'
  })
}

// 新增课题参与人员
export function addUser(data) {
  return request({
    url: '/trainplatform/project/user',
    method: 'post',
    data: data
  })
}

// 批量新增课题参与人员
export function batchAdd(data) {
  return request({
    url: '/trainplatform/project/user/batchAdd',
    method: 'post',
    data: data
  })
}

// 修改课题参与人员
export function updateUser(data) {
  return request({
    url: '/trainplatform/project/user',
    method: 'put',
    data: data
  })
}

// 删除课题参与人员
export function delUser(id) {
  return request({
    url: '/trainplatform/project/user/' + id,
    method: 'delete'
  })
}
