import request from '@/utils/request'

// 查询课题列表
export function listProject(query) {
  return request({
    url: '/trainplatform/project/list',
    method: 'post',
    data: query
  })
}

// 查询课题详细
export function getProject(id) {
  return request({
    url: '/trainplatform/project/' + id,
    method: 'get'
  })
}

// 新增课题
export function addProject(data) {
  return request({
    url: '/trainplatform/project',
    method: 'post',
    data: data
  })
}

// 修改课题
export function updateProject(data) {
  return request({
    url: '/trainplatform/project',
    method: 'put',
    data: data
  })
}

// 删除课题
export function delProject(id) {
  return request({
    url: '/trainplatform/project/' + id,
    method: 'delete'
  })
}
