import request from '@/utils/request'

// 查询患者文件资料存储列表
export function listFiles(query) {
  return request({
    url: '/trainplatform/files/list',
    method: 'get',
    params: query
  })
}

// 查询患者文件资料存储详细
export function getFiles(id) {
  return request({
    url: '/trainplatform/files/' + id,
    method: 'get'
  })
}

// 新增患者文件资料存储
export function addFiles(data) {
  return request({
    url: '/trainplatform/files',
    method: 'post',
    data: data
  })
}

// 修改患者文件资料存储
export function updateFiles(data) {
  return request({
    url: '/trainplatform/files',
    method: 'put',
    data: data
  })
}

// 删除患者文件资料存储
export function delFiles(id) {
  return request({
    url: '/trainplatform/files/' + id,
    method: 'delete'
  })
}
