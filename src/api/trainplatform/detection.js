import request from '@/utils/request'

// 查询脑功能成像检测列表
export function listDetection(query) {
  return request({
    url: '/trainplatform/detection/list',
    method: 'get',
    params: query
  })
}

// 查询脑功能成像检测详细
export function getDetection(id) {
  return request({
    url: '/trainplatform/detection/' + id,
    method: 'get'
  })
}

// 新增脑功能成像检测
export function addDetection(data) {
  return request({
    url: '/trainplatform/detection',
    method: 'post',
    data: data
  })
}

// 修改脑功能成像检测
export function updateDetection(data) {
  return request({
    url: '/trainplatform/detection',
    method: 'put',
    data: data
  })
}

// 删除脑功能成像检测
export function delDetection(id) {
  return request({
    url: '/trainplatform/detection/' + id,
    method: 'delete'
  })
}
