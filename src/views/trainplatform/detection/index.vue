<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户id" prop="patientUserId">
        <el-input
          v-model="queryParams.patientUserId"
          placeholder="请输入用户id"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="对应的用户推理ID" prop="userInferenceId">
        <el-input
          v-model="queryParams.userInferenceId"
          placeholder="请输入对应的用户推理ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检查日期(年/月/日)" prop="detectionDate">
        <el-date-picker clearable
          v-model="queryParams.detectionDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择检查日期(年/月/日)">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="未完成的原因" prop="reason">
        <el-input
          v-model="queryParams.reason"
          placeholder="请输入未完成的原因"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备型号" prop="deviceModel">
        <el-input
          v-model="queryParams.deviceModel"
          placeholder="请输入设备型号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="记录时长(s)" prop="duration">
        <el-input
          v-model="queryParams.duration"
          placeholder="请输入记录时长(s)"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建日期" prop="createTime">
        <el-date-picker clearable
          v-model="queryParams.createTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择创建日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间" prop="updateTime">
        <el-date-picker clearable
          v-model="queryParams.updateTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择更新时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['trainplatform:detection:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['trainplatform:detection:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['trainplatform:detection:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['trainplatform:detection:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="detectionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="用户id" align="center" prop="patientUserId" />
      <el-table-column label="对应的用户推理ID" align="center" prop="userInferenceId" />
      <el-table-column label="检查日期(年/月/日)" align="center" prop="detectionDate" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.detectionDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否完成检查1是2否" align="center" prop="finishStatus" />
      <el-table-column label="未完成的原因" align="center" prop="reason" />
      <el-table-column label="设备型号" align="center" prop="deviceModel" />
      <el-table-column label="记录时长(s)" align="center" prop="duration" />
      <el-table-column label="结局事件" align="center" prop="resultEvent" />
      <el-table-column label="创建日期" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['trainplatform:detection:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['trainplatform:detection:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改脑功能成像检测对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="detectionRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户id" prop="patientUserId">
          <el-input v-model="form.patientUserId" placeholder="请输入用户id" />
        </el-form-item>
        <el-form-item label="对应的用户推理ID" prop="userInferenceId">
          <el-input v-model="form.userInferenceId" placeholder="请输入对应的用户推理ID" />
        </el-form-item>
        <el-form-item label="检查日期(年/月/日)" prop="detectionDate">
          <el-date-picker clearable
            v-model="form.detectionDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择检查日期(年/月/日)">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="未完成的原因" prop="reason">
          <el-input v-model="form.reason" placeholder="请输入未完成的原因" />
        </el-form-item>
        <el-form-item label="设备型号" prop="deviceModel">
          <el-input v-model="form.deviceModel" placeholder="请输入设备型号" />
        </el-form-item>
        <el-form-item label="记录时长(s)" prop="duration">
          <el-input v-model="form.duration" placeholder="请输入记录时长(s)" />
        </el-form-item>
        <el-form-item label="结局事件" prop="resultEvent">
          <el-input v-model="form.resultEvent" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Detection">
import { listDetection, getDetection, delDetection, addDetection, updateDetection } from "@/api/trainplatform/detection";

const { proxy } = getCurrentInstance();

const detectionList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    patientUserId: null,
    userInferenceId: null,
    detectionDate: null,
    finishStatus: null,
    reason: null,
    deviceModel: null,
    duration: null,
    resultEvent: null,
    createTime: null,
    updateTime: null
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询脑功能成像检测列表 */
function getList() {
  loading.value = true;
  listDetection(queryParams.value).then(response => {
    detectionList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    patientUserId: null,
    userInferenceId: null,
    detectionDate: null,
    finishStatus: null,
    reason: null,
    deviceModel: null,
    duration: null,
    resultEvent: null,
    createTime: null,
    updateTime: null
  };
  proxy.resetForm("detectionRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加脑功能成像检测";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getDetection(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改脑功能成像检测";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["detectionRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateDetection(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addDetection(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除脑功能成像检测编号为"' + _ids + '"的数据项？').then(function() {
    return delDetection(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('trainplatform/detection/export', {
    ...queryParams.value
  }, `detection_${new Date().getTime()}.xlsx`)
}

getList();
</script>
