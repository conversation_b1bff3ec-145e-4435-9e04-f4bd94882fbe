<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="brxm">
        <el-input v-model="queryParams.brxm" placeholder="请输入姓名" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="性别" prop="brxb">
        <el-select v-model="queryParams.brxb" placeholder="请选择性别" clearable style="width: 180px">
          <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['trainplatform:patient:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['trainplatform:patient:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['trainplatform:patient:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload"
          @click="handleIExcelmport"
          v-hasPermi="['trainplatform:patient:import']"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['trainplatform:patient:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="patientList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="用户id" align="center" prop="userId" width="100" />
      <el-table-column label="HIS号" align="center" prop="brid" />
      <el-table-column label="姓名" align="center" prop="brxm" width="80" />
      <el-table-column label="性别" align="center" prop="brxb" width="60">
        <template #default="scope">
          <dict-tag :options="sys_user_sex" :value="scope.row.brxb" />
        </template>
      </el-table-column>
      <el-table-column label="出生年月" align="center" prop="csny" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.csny, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Tickets"
            @click="handleNapiManage(scope.row)"
            v-hasPermi="['trainplatform:patient:napiManage']"
            >评估管理</el-button
          >
          <el-button
            link
            type="primary"
            icon="Document"
            @click="handleFileList(scope.row)"
            v-hasPermi="['trainplatform:patient:fileList']"
            >文件</el-button
          >
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['trainplatform:patient:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['trainplatform:patient:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改病人管理对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="patientRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="HIS号" prop="brid">
          <el-input v-model="form.brid" placeholder="请输入HIS号" />
        </el-form-item>
        <el-form-item label="姓名" prop="brxm">
          <el-input v-model="form.brxm" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="性别" prop="brxb">
          <el-select v-model="form.brxb" placeholder="请选择性别">
            <el-option
              v-for="dict in sys_user_sex"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="出生年月" prop="csny">
          <el-date-picker
            clearable
            v-model="form.csny"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择出生年月"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 病人导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleExcelUploadProgress"
        :on-success="handleExcelSuccess"
        :on-exceed="handleExceed"
        :auto-upload="false"
        :show-file-list="true"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip"><el-checkbox v-model="upload.updateSupport" />是否更新已经存在的病人数据</div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitExcelForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 文件列表 -->
    <el-dialog :title="title" v-model="napiFileListDialog" width="800px" style="height: 700px" append-to-body>
      <el-table v-loading="loading" :data="filesList">
        <el-table-column label="id" align="center" prop="id" width="60" />
        <el-table-column label="视频采集id" align="center" prop="bizId" width="100" />
        <el-table-column label="文件名称" align="center" prop="fileName" />
        <el-table-column label="文件大小(M)" align="center" prop="fileSize">
          <template #default="scope">
            {{ formatFileSize(scope.row.fileSize) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Search"
              @click="handlePreview(scope.row)"
              v-hasPermi="['trainplatform:files:down']"
              >预览</el-button
            >
            <el-button
              link
              type="primary"
              icon="Document"
              @click="handleDownload(scope.row)"
              v-hasPermi="['trainplatform:files:down']"
              >下载</el-button
            >
            <el-button
              link
              type="primary"
              icon="Delete"
              @click="handleDeleteFile(scope.row)"
              v-hasPermi="['trainplatform:files:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog :title="title" v-model="napiFilePreview" width="700px">
      <!-- 视频预览播放器 -->
      <video v-if="previewVideoUrl" controls style="width: 100%; max-width: 800px; margin-top: 20px">
        <source :src="previewVideoUrl" type="video/mp4" />
        您的浏览器不支持 HTML5 视频,可更换浏览器进行尝试。
      </video>
    </el-dialog>
    <!-- 文件上传对话框 -->
    <el-dialog :title="uploadFile.title" v-model="uploadFile.open" width="400px" append-to-body>
      <el-upload
        ref="uploadFileRef"
        :limit="1"
        accept=".mp4, .wav"
        :headers="uploadFile.headers"
        :action="uploadFile.url"
        :disabled="uploadFile.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-exceed="handleExceed"
        :auto-upload="false"
        :http-request="uploadFileRequest"
        :on-remove="handleFileRemove"
        :file-list="uploadFileList"
        :show-file-list="true"
        :before-upload="beforeFileUpload"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>仅允许上传.mp4、.wav格式文件。</span>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="uploadFile.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 评估管理 -->
    <el-dialog :title="title" v-model="napiManageDialog" width="900px" style="height: 700px" append-to-body>
      <el-table v-loading="loading" :data="aimpNapiCollectionList" @selection-change="handleSelectionChange">
        <!-- <el-table-column label="用户id" align="center" prop="patientUserId" width="70" /> -->
        <el-table-column label="id" align="center" prop="id" width="80" />
        <el-table-column label="采集类型" align="center" prop="collectionType" width="80" />

        <el-table-column label="评估类型" align="center" prop="assessmentType">
          <template #default="scope">
            <dict-tag :options="aimp_assessment_type" :value="scope.row.assessmentType" />
          </template>
        </el-table-column>
        <el-table-column label="AI评估结果" align="center" prop="aiAssessmentResult">
          <template #default="scope">
            <dict-tag :options="assessment_type" :value="scope.row.aiAssessmentResult" />
          </template>
        </el-table-column>
        <el-table-column label="医生评估结果-阶段一" align="center" prop="doctorAssessmentStep1">
          <template #default="scope">
            <dict-tag :options="doctor_assessment_1_type" :value="scope.row.doctorAssessmentStep1" />
          </template>
        </el-table-column>
        <el-table-column label="医生评估结果-阶段二" align="center" prop="doctorAssessmentStep2">
          <template #default="scope">
            <dict-tag :options="assessment_type" :value="scope.row.doctorAssessmentStep2" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="350">
          <template #default="scope">
            <!-- <el-button link type="primary"  @click="handleUpdate(scope.row)" v-hasPermi="['trainplatform:napiCollection:edit']">查看结论</el-button>
            <el-button link type="primary"  @click="handleUpdate(scope.row)" v-hasPermi="['trainplatform:napiCollection:edit']">文件预览</el-button>
            <el-button link type="primary"  @click="handleUpdate(scope.row)" v-hasPermi="['trainplatform:napiCollection:edit']">打包下载</el-button> -->
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleNapiEidt(scope.row)"
              v-hasPermi="['trainplatform:napiCollection:edit']"
              >修改</el-button
            >
            <el-button
              link
              type="primary"
              icon="Delete"
              @click="handleNapiDelete(scope.row)"
              v-hasPermi="['trainplatform:napiCollection:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 修改动态视频采集记录对话框 -->
    <el-dialog :title="title" v-model="napiEditDialog" width="800px" append-to-body>
      <el-form ref="napiCollectionRef" :model="napiForm" :rules="napiRules" label-width="150px" inline>
        <el-form-item label="gms病历ID" prop="gmsId">
          <el-input v-model="napiForm.gmsId" placeholder="请输入gms病历ID" />
        </el-form-item>
        <el-form-item label="就诊号码" prop="jzhm">
          <el-input v-model="napiForm.jzhm" placeholder="请输入就诊号码" />
        </el-form-item>
        <el-form-item label="住院号" prop="zyh">
          <el-input v-model="napiForm.zyh" placeholder="请输入住院号" />
        </el-form-item>
        <el-form-item label="用户推理ID" prop="userInferenceId">
          <el-input v-model="napiForm.userInferenceId" placeholder="请输入用户推理ID" />
        </el-form-item>
        <el-form-item label="采集类型" prop="collectionType">
          <el-input v-model="napiForm.collectionType" placeholder="请输入采集类型" />
        </el-form-item>
        <el-form-item label="测量日期" prop="collectionDate">
          <el-date-picker
            clearable
            v-model="napiForm.collectionDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择测量日期"
            style="width: 173px"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="纠正胎龄" prop="correctAge" type="number">
          <el-input v-model="napiForm.correctAge" placeholder="请输入纠正胎龄" type />
        </el-form-item>
        <el-form-item label="视频地址" prop="videoUrl">
          <img v-if="UploadingFileFlag == true" :src="loadingGif" alt="Loading..." />
          <el-text v-if="UploadingFileFlag == false">{{ napiForm.videoFileName }}</el-text>
          <el-button type="info" plain icon="Upload" @click="handleUploadFile" style="margin-left: 20px">
            <el-text v-if="napiForm.videoUrl != null">重新上传</el-text>
            <el-text v-if="napiForm.videoUrl == null">上传</el-text>
          </el-button>
          <!-- <el-text v-if="aimpPatientFiles.fileName!=null">{{aimpPatientFiles.fileName}}</el-text>             
              <el-text v-if="aimpPatientFiles.fileName==null">{{napiForm.videoFileName}}</el-text> -->
        </el-form-item>
        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="napiForm.deviceName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备型号" prop="deviceModel">
          <el-input v-model="napiForm.deviceModel" placeholder="请输入设备型号" />
        </el-form-item>
        <el-form-item label="体重(kg)" prop="weight">
          <el-input v-model="napiForm.weight" placeholder="请输入体重(kg)" />
        </el-form-item>
        <el-form-item label="身长(cm)" prop="height">
          <el-input v-model="napiForm.height" placeholder="请输入身长(cm)" />
        </el-form-item>
        <el-form-item label="体温" prop="bodyTemperature">
          <el-input v-model="napiForm.bodyTemperature" placeholder="请输入体温" />
        </el-form-item>
        <el-form-item label="心率" prop="hr">
          <el-input v-model="napiForm.hr" placeholder="请输入心率" />
        </el-form-item>
        <el-form-item label="呼吸频率" prop="rr">
          <el-input v-model="napiForm.rr" placeholder="请输入呼吸频率" />
        </el-form-item>
        <el-form-item label="舒张压" prop="diastolicPressure">
          <el-input v-model="napiForm.diastolicPressure" placeholder="请输入舒张压" />
        </el-form-item>
        <el-form-item label="收缩压" prop="systolicPressure">
          <el-input v-model="napiForm.systolicPressure" placeholder="请输入收缩压" />
        </el-form-item>
        <el-form-item label="视频质量" prop="videoQuality">
          <el-radio-group v-model="napiForm.videoQuality">
            <el-radio v-for="dict in aimp_video_quality" :key="dict.value" :label="parseInt(dict.value)">{{
              dict.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="评估类型" prop="assessmentType">
          <el-radio-group v-model="napiForm.assessmentType">
            <el-radio v-for="dict in aimp_assessment_type" :key="dict.value" :label="parseInt(dict.value)">{{
              dict.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="AI评估结果" prop="aiAssessmentResult">
          <el-radio-group v-model="napiForm.aiAssessmentResult">
            <el-radio v-for="dict in assessment_type" :key="dict.value" :label="parseInt(dict.value)">{{
              dict.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="医生评估结果-阶段一" prop="doctorAssessmentStep1">
          <el-radio-group v-model="napiForm.doctorAssessmentStep1">
            <el-radio v-for="dict in doctor_assessment_1_type" :key="dict.value" :label="parseInt(dict.value)">{{
              dict.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="医生评估结果-阶段二" prop="doctorAssessmentStep2">
          <el-radio-group v-model="napiForm.doctorAssessmentStep2">
            <el-radio v-for="dict in assessment_type" :key="dict.value" :label="parseInt(dict.value)">{{
              dict.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="医生评估结果-其他" prop="doctorAssessmentOthers">
          <el-radio-group v-model="napiForm.doctorAssessmentOthers">
            <el-radio v-for="dict in doctor_assessment_others" :key="dict.value" :label="parseInt(dict.value)">{{
              dict.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-row style="width: 100%">
          <el-col :span="24">
            <el-form-item label="预约下次评估时间" prop="nextAssessmentTime">
              <el-date-picker
                clearable
                v-model="napiForm.nextAssessmentTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择预约下次评估时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="width: 100%">
          <el-col :span="24">
            <el-form-item label="早期干预建议" prop="suggestion">
              <el-input v-model="napiForm.suggestion" type="textarea" placeholder="请输入内容" style="width: 525px" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="napiForm.remark" type="textarea" placeholder="请输入内容" style="width: 525px" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitNapiForm">确 定</el-button>
          <el-button @click="cancelNapi">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Patient">
import loadingGif from '@/assets/images/loading.gif'
import { getToken } from '@/utils/auth'
import { listPatient, getPatient, delPatient, addPatient, updatePatient } from '@/api/trainplatform/patient'
import {
  listNapiCollection,
  getNapiCollection,
  delNapiCollection,
  addNapiCollection,
  updateNapiCollection
} from '@/api/trainplatform/napiCollection'
import { listFiles, delFiles } from '@/api/trainplatform/files'
import axios from 'axios'

const { proxy } = getCurrentInstance()
const {
  aimp_video_quality,
  sys_yes_no,
  sys_user_sex,
  doctor_assessment_others,
  doctor_assessment_1_type,
  assessment_type,
  aimp_assessment_type
} = proxy.useDict(
  'aimp_video_quality',
  'sys_yes_no',
  'sys_user_sex',
  'doctor_assessment_others',
  'doctor_assessment_1_type',
  'assessment_type',
  'aimp_assessment_type'
)

const patientList = ref([])
const aimpNapiCollectionList = ref([])

/***弹窗显示标识位 */
const open = ref(false)
const napiManageDialog = ref(false)
const napiEditDialog = ref(false)
const napiFileListDialog = ref(false)
const napiFilePreview = ref(false)
const loading = ref(true)
const showSearch = ref(true)

const ids = ref([])
const checkedAimpNapiCollection = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref('')
const uploadFileList = ref([])
const aimpPatientFiles = ref({})
const responsePatientFiles = ref({})
const UploadingFileFlag = ref(false)
const filesList = ref([])

const previewVideoUrl = ref(null)
var patientId

/*** 病人导入参数 */
const upload = reactive({
  // 是否显示弹出层（病人导入）
  open: false,
  // 弹出层标题（病人导入）
  title: '',
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的病人数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { Authorization: 'Bearer ' + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + '/trainplatform/patient/importData'
})

/*** 上传文件参数 */
const uploadFile = reactive({
  // 是否显示弹出层（上传文件）
  open: false,
  // 弹出层标题
  title: '文件上传',
  // 是否禁用上传
  isUploading: false,
  // 设置上传的请求头部
  headers: { Authorization: 'Bearer ' + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + '/trainplatform/upload/preSignUploadUrl'
})
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    brxm: null,
    brxb: null
  },
  rules: {
    brxm: [
      { required: true, message: '姓名不能为空', trigger: 'blur' },
      { max: 20, message: '最多输入 20 个字符', trigger: 'blur' }
    ],
    brid: [{ max: 30, message: '最多输入 30 个字符', trigger: 'blur' }]
  }
})

const { queryParams, form, rules } = toRefs(data)

const napiData = reactive({
  napiForm: {},
  queryNapiParams: {
    pageNum: 1,
    pageSize: 999,
    jzhm: null,
    zyh: null,
    patientUserId: null,
    gmsId: null,
    userInferenceId: null,
    collectionType: null,
    collectionDate: null,
    correctAge: null,
    videoUrl: null,
    deviceName: null,
    deviceModel: null,
    weight: null,
    height: null,
    bodyTemperature: null,
    hr: null,
    rr: null,
    diastolicPressure: null,
    systolicPressure: null,
    videoQuality: null,
    assessmentType: null,
    aiAssessmentResult: null,
    doctorUserId: null,
    doctorAssessmentStep1: null,
    doctorAssessmentStep2: null,
    doctorAssessmentOthers: null,
    suggestion: null,
    nextAssessmentTime: null,
    createdTime: null,
    updatedTime: null,
    createUser: null,
    updateUser: null,
    collectionUserName: null
  },
  napiRules: {
    gmsId: [{ required: true, message: 'gms系统病历ID不能为空', trigger: 'blur' }],
    userInferenceId: [{ pattern: /^\d+$/, message: '用户推理ID只能输入数字', trigger: 'blur' }],
    zyh: [{ pattern: /^\d+$/, message: '住院号只能输入数字', trigger: 'blur' }],
    correctAge: [{ pattern: /^\d+$/, message: '纠正胎龄只能输入数字', trigger: 'blur' }],
    weight: [{ pattern: /^\d+(\.\d{1,2})?$/, message: '请输入数字，最多 2 位小数', trigger: 'blur' }],
    height: [{ pattern: /^\d+(\.\d{1,2})?$/, message: '请输入数字，最多 2 位小数', trigger: 'blur' }],
    correctAge: [{ pattern: /^\d+$/, message: '体重只能输入数字', trigger: 'blur' }],
    bodyTemperature: [{ pattern: /^\d+(\.\d{1,2})?$/, message: '请输入数字，最多 2 位小数', trigger: 'blur' }],
    hr: [{ pattern: /^\d+$/, message: '心率只能输入数字', trigger: 'blur' }],
    rr: [{ pattern: /^\d+$/, message: '呼吸频率只能输入数字', trigger: 'blur' }],
    diastolicPressure: [{ pattern: /^\d+$/, message: '舒张压只能输入数字', trigger: 'blur' }],
    systolicPressure: [{ pattern: /^\d+$/, message: '收缩压只能输入数字', trigger: 'blur' }],

    jzhm: [{ max: 16, message: '最多输入 16 个字符', trigger: 'blur' }],
    gmsId: [{ max: 32, message: '最多输入 32 个字符', trigger: 'blur' }],
    collectionType: [{ max: 128, message: '最多输入 128 个字符', trigger: 'blur' }],
    videoUrl: [{ max: 128, message: '最多输入 128 个字符', trigger: 'blur' }],
    deviceName: [{ max: 128, message: '最多输入 128 个字符', trigger: 'blur' }],
    deviceModel: [{ max: 128, message: '最多输入 128 个字符', trigger: 'blur' }],
    remark: [{ max: 128, message: '最多输入 128 个字符', trigger: 'blur' }],
    collectionUserName: [{ max: 20, message: '最多输入 20 个字符', trigger: 'blur' }]
  }
})

const { queryNapiParams, napiForm, napiRules } = toRefs(napiData)

/** 查询病人管理列表 */
function getList() {
  loading.value = true
  listPatient(queryParams.value).then((response) => {
    patientList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 查询病人评估列表 */
function getNapiList() {
  loading.value = true
  queryNapiParams.value.pageNum = 1
  const _id = patientId //napiForm.value.patientUserId
  getPatient(_id).then((response) => {
    form.value = response.data
    aimpNapiCollectionList.value = response.data.aimpNapiCollectionList
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 取消评估按钮
function cancelNapi() {
  napiEditDialog.value = false
  resetNapi()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    userId: null,
    brid: null,
    brxm: null,
    brxb: null,
    csny: null,
    deleted: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
    remark: null
  }
  proxy.resetForm('patientRef')
}

// 表单重置
function resetNapi() {
  form.value = {
    id: null,
    patientUserId: null,
    jzhm: null,
    zyh: null,
    gmsId: null,
    userInferenceId: null,
    collectionType: null,
    collectionDate: null,
    correctAge: null,
    videoUrl: null,
    deviceName: null,
    deviceModel: null,
    weight: null,
    height: null,
    bodyTemperature: null,
    hr: null,
    rr: null,
    diastolicPressure: null,
    systolicPressure: null,
    videoQuality: null,
    assessmentType: null,
    aiAssessmentResult: null,
    doctorUserId: null,
    doctorAssessmentStep1: null,
    doctorAssessmentStep2: null,
    doctorAssessmentOthers: null,
    suggestion: null,
    nextAssessmentTime: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
    remark: null
  }
  proxy.resetForm('napiCollectionRef')
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef')
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = '添加病人管理'
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getPatient(_id).then((response) => {
    form.value = response.data
    open.value = true
    title.value = '修改病人管理'
  })
}

/** 详情按钮操作 */
function handleGetInfo(row) {
  const _id = row.id || ids.value
  getPatient(_id).then((response) => {
    form.value = response.data
    aimpNapiCollectionList.value = response.data.aimpNapiCollectionList
    open.value = true
    title.value = '病人信息'
  })
}

/** 评估管理 */
function handleNapiManage(row) {
  resetNapi()
  queryNapiParams.value.pageNum = 1
  const _id = row.id || ids.value
  patientId = _id
  getPatient(_id).then((response) => {
    form.value = response.data
    aimpNapiCollectionList.value = response.data.aimpNapiCollectionList
    napiManageDialog.value = true
    title.value = '评估管理'
  })
}

/** 文件列表 */
function handleFileList(row) {
  const _id = row.id || ids.value
  var patientFiles = { patientUserId: row.userId }
  listFiles(patientFiles).then((response) => {
    filesList.value = response.rows
    napiFileListDialog.value = true
    title.value = '文件列表'
  })
}

/** 编辑评估 */
function handleNapiEidt(row) {
  // getPatient(_id).then(response => {
  //   form.value = response.data;
  //   aimpNapiCollectionList.value = response.data.aimpNapiCollectionList;
  //   napiEditDialog.value = true;
  //   title.value = "修改评估";
  // });
  resetNapi()
  const _id = row.id || ids.value
  getNapiCollection(_id).then((response) => {
    napiForm.value = response.data
    napiEditDialog.value = true
    title.value = '修改动态视频采集记录'
  })
}

/** 删除评估 */
function handleNapiDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal
    .confirm('是否确认删除动态视频采集记录编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delNapiCollection(_ids)
    })
    .then(() => {
      getNapiList()
      proxy.$modal.msgSuccess('删除成功')
    })
    .catch(() => {})
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['patientRef'].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updatePatient(form.value).then((response) => {
          proxy.$modal.msgSuccess('修改成功')
          open.value = false
          getList()
        })
      } else {
        addPatient(form.value).then((response) => {
          proxy.$modal.msgSuccess('新增成功')
          open.value = false
          getList()
        })
      }
    }
  })
}

function submitNapi() {
  proxy.$refs['napiCollectionRef'].validate((valid) => {
    if (valid) {
      if (napiForm.value.id != null) {
        updateNapiCollection(napiForm.value).then((response) => {
          proxy.$modal.msgSuccess('修改评估成功')
          napiEditDialog.value = false
          getNapiList()
        })
      } else {
        addNapiCollection(napiForm.value).then((response) => {
          proxy.$modal.msgSuccess('新增评估成功')
          napiEditDialog.value = false
          getNapiList()
        })
      }
    }
  })
}
/** 提交评估按钮 */
function submitNapiForm() {
  if (UploadingFileFlag.value == true) {
    proxy
      .$confirm('文件上传中，继续提交可能上传失败!', '提示', {
        confirmButtonText: '提交',
        cancelButtonText: '取消',
        type: 'warning'
      })
      .then(() => {
        submitNapi()
      })
      .catch(() => {
        proxy.$message({
          type: 'info',
          message: '操作已取消'
        })
        return
      })
  } else {
    submitNapi()
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal
    .confirm('是否确认删除病人管理编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delPatient(_ids)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess('删除成功')
    })
    .catch(() => {})
}

/** 动态视频采集记录序号 */
function rowAimpNapiCollectionIndex({ row, rowIndex }) {
  row.index = rowIndex + 1
}

/** 动态视频采集记录添加按钮操作 */
function handleAddAimpNapiCollection() {
  let obj = {}
  obj.gmsId = ''
  obj.userInferenceId = ''
  obj.collectionType = ''
  obj.collectionDate = ''
  obj.collectionUserName = ''
  obj.correctAge = ''
  obj.videoUrl = ''
  obj.deviceName = ''
  obj.deviceModel = ''
  obj.weight = ''
  obj.height = ''
  obj.bodyTemperature = ''
  obj.hr = ''
  obj.rr = ''
  obj.diastolicPressure = ''
  obj.systolicPressure = ''
  obj.videoQuality = ''
  obj.assessmentType = ''
  obj.aiAssessmentResult = ''
  obj.doctorUserId = ''
  obj.doctorAssessmentStep1 = ''
  obj.doctorAssessmentStep2 = ''
  obj.doctorAssessmentOthers = ''
  obj.suggestion = ''
  obj.nextAssessmentTime = ''
  obj.updateTime = ''
  obj.remark = ''
  aimpNapiCollectionList.value.push(obj)
}

/** 动态视频采集记录删除按钮操作 */
function handleDeleteAimpNapiCollection() {
  if (checkedAimpNapiCollection.value.length == 0) {
    proxy.$modal.msgError('请先选择要删除的动态视频采集记录数据')
  } else {
    const aimpNapiCollections = aimpNapiCollectionList.value
    const checkedAimpNapiCollections = checkedAimpNapiCollection.value
    aimpNapiCollectionList.value = aimpNapiCollections.filter(function (item) {
      return checkedAimpNapiCollections.indexOf(item.index) == -1
    })
  }
}

/** 复选框选中数据 */
function handleAimpNapiCollectionSelectionChange(selection) {
  checkedAimpNapiCollection.value = selection.map((item) => item.index)
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download('trainplatform/patient/importTemplate', {}, `patient_template_${new Date().getTime()}.xlsx`)
}

/** 导入按钮操作 */
function handleIExcelmport() {
  upload.title = '病人导入'
  upload.open = true
}

/**excel上传中处理 */
const handleExcelUploadProgress = (event, file, fileList) => {
  upload.isUploading = true
}

/** excel上传成功处理 */
const handleExcelSuccess = (response, file, fileList) => {
  upload.open = false
  upload.isUploading = false
  proxy.$refs['uploadRef'].handleRemove(file)
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + '</div>',
    '导入结果',
    { dangerouslyUseHTMLString: true, customClass: 'custom-alert-box' }
  )
  getList()
}

/** 提交上传excel */
function submitExcelForm() {
  proxy.$refs['uploadRef'].submit()
}

/** 上传文件按钮操作 */
function handleUploadFile() {
  uploadFile.title = '文件上传'
  uploadFile.open = true
  let patientFiles = { bizId: napiForm.value.id }
  listFiles(patientFiles).then((response) => {
    if (response.rows && response.rows.length > 0 && response.rows[0]) {
      responsePatientFiles.value = response.rows[0]
    }
  })
}

// 监听删除文件
const handleFileRemove = (file, files) => {
  uploadFileList.value = files // 更新 uploadFileList
}

// 打开上传时，手动清空 uploadFileList
const beforeFileUpload = (file) => {
  uploadFileList.value = [] // 重新选择时清空
  return true // 允许上传
}

function generateUUID32() {
  let chars = '0123456789abcdef'
  let uuid = ''
  for (let i = 0; i < 32; i++) {
    uuid += chars[Math.floor(Math.random() * 16)]
  }
  return uuid
}

const uploadFileRequest = async (fileData) => {
  uploadFile.open = false
  UploadingFileFlag.value = true
  const file = fileData.file

  // 设置文件的关键信息
  aimpPatientFiles.fileKey = generateUUID32()
  aimpPatientFiles.patientUserId = napiForm.value.patientUserId
  aimpPatientFiles.source = '101'
  aimpPatientFiles.bizId = napiForm.value.id
  aimpPatientFiles.fileName = file.name
  aimpPatientFiles.fileFormat = file.name.substring(file.name.lastIndexOf('.') + 1)
  aimpPatientFiles.fileSize = file.size

  try {
    // 1️⃣ **使用 GET 请求获取预签名 URL**
    //uploadFile.url = import.meta.env.VITE_APP_BASE_API + '/trainplatform/upload/preSignUploadUrl?source=101&objectName='+file.name;
    console.log(' uploadFile.url', uploadFile.url)

    const response = await axios.get(uploadFile.url, {
      params: {
        source: '101',
        objectName: aimpPatientFiles.fileKey
      },
      headers: {
        Authorization: 'Bearer ' + getToken()
      }
    })

    let uploadUrl = response.data // 上传地址 & 访问地址

    console.log('获取到的预上传 URL:', uploadUrl)

    // 2️⃣ **使用 `PUT` 方法直接上传文件**
    await axios.put(uploadUrl, file, {
      headers: {
        'Content-Type': file.type
      }
    })
    proxy.$modal.msgSuccess('文件上传成功')
    console.log('文件上传成功!')

    // 3️⃣ **通知后端文件已上传**
    let uploadNoticeUrl
    uploadNoticeUrl = import.meta.env.VITE_APP_BASE_API + '/trainplatform/files'

    try {
      if (napiForm.value.videoUrl !== null && napiForm.value.videoUrl !== undefined) {
        uploadNoticeUrl = import.meta.env.VITE_APP_BASE_API + '/trainplatform/files/edit'
        aimpPatientFiles.id = responsePatientFiles.value.id
      }
      await axios.post(uploadNoticeUrl, aimpPatientFiles, {
        headers: {
          Authorization: 'Bearer ' + getToken()
        }
      })
      napiForm.value.videoFileName = aimpPatientFiles.fileName
      console.log('✅ 后端通知成功:', response.data)
      uploadFileList.value = [] // 清空 uploadFileList
    } catch (error) {
      console.error('❌ 通知后端失败:', error)
    }
  } catch (error) {
    console.error('上传失败:', error)
  } finally {
    UploadingFileFlag.value = false
  }
}
/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  uploadFile.isUploading = true
}

function handleExceed(files, fileList) {
  proxy.$message.warning('只能上传一个文件，请先删除原来文件')
}

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs['uploadFileRef'].submit()
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'trainplatform/patient/export',
    {
      ...queryParams.value
    },
    `patient_${new Date().getTime()}.xlsx`
  )
}

getList()

function formatFileSize(bytes) {
  if (bytes === 0) return '0 M'
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`
}

function handleDownload(row) {
  if (row.fileKey) {
    getDownloadUrl(row.fileKey)
      .then((downloadUrl) => {
        proxy.$message.warning('正在下载文件请稍后...')
        window.open(downloadUrl, '_blank') // 在新标签页打开
        // downloadFile(downloadUrl, row.fileName);
      })
      .catch((error) => {
        console.error('下载失败', error.message)
        proxy.$message.error('下载失败: ' + error.message)
      })
  } else {
    console.error('文件键（fileKey）未定义，无法下载文件')
    proxy.$message.error('文件键未定义，无法下载文件')
  }
}

// 获取下载URL的方法
const getDownloadUrl = async (fileKey) => {
  const apiUrl =
    import.meta.env.VITE_APP_BASE_API + '/trainplatform/upload/preSignDownloadUrl?source=101&objectName=' + fileKey

  try {
    const response = await axios.get(apiUrl, {
      headers: {
        Authorization: 'Bearer ' + getToken()
      }
    })

    if (response.status === 200) {
      return response.data // 返回下载URL
    } else {
      throw new Error(response.data.msg || '获取下载链接失败')
    }
  } catch (error) {
    console.error('请求失败', error.message)
    throw error
  }
}

// 获取下载URL的方法
// const getPlayUrl = async (fileKey) => {
//   const apiUrl =
//     import.meta.env.VITE_APP_BASE_API +
//     "trainplatform/upload/preSignPlayUrl?source=101&objectName=" +
//     fileKey;

//   try {
//     const response = await axios.get(apiUrl, {
//       headers: {
//         Authorization: 'Bearer ' + getToken()
//       },
//     });

//     if (response.status === 200) {
//       return response.data; // 返回下载URL
//     } else {
//       throw new Error(response.data.msg || "获取下载链接失败");
//     }
//   } catch (error) {
//     console.error("请求失败", error.message);
//     throw error;
//   }
// };

// 下载文件的方法
// const downloadFile = async (url, fileName) => {
//   try {
//     const response = await axios.get(url, {
//       responseType: "blob", // 以二进制流方式接收
//     });

//     const blob = new Blob([response.data], { type: "video/mp4" }); // 指定 MIME 类型
//     const urlObject = window.URL.createObjectURL(blob);

//     const a = document.createElement("a");
//     a.href = urlObject;
//     a.download = fileName; // 指定文件名，确保下载
//     document.body.appendChild(a);
//     a.click();
//     proxy.$message.success("下载成功");

//     // 清理资源
//     window.URL.revokeObjectURL(urlObject);
//     document.body.removeChild(a);
//   } catch (error) {
//     console.error("下载失败", error.message);
//   }
// };

// 视频预览函数
function handlePreview(row) {
  if (row.fileKey) {
    getDownloadUrl(row.fileKey)
      .then((downloadUrl) => {
        previewVideoUrl.value = downloadUrl
        console.log('previewVideoUrl.value=' + previewVideoUrl.value)
        napiFilePreview.value = true
        title.value = '视频预览'
      })
      .catch((error) => {
        console.error('下载失败', error.message)
        proxy.$message.error('下载失败: ' + error.message)
      })
  } else {
    console.error('文件键（fileKey）未定义，无法下载文件')
    proxy.$message.error('文件键未定义，无法下载文件')
  }
}

function handleDeleteFile(row) {
  const _ids = row.id || ids.value
  proxy.$modal
    .confirm('是否确认删除患者文件资料存储编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delFiles(_ids)
    })
    .then(() => {
      var patientFiles = { patientUserId: row.patientUserId }
      listFiles(patientFiles).then((response) => {
        filesList.value = response.rows
      })
      proxy.$modal.msgSuccess('删除成功')
    })
    .catch(() => {})
}
</script>
<style lang="css">
/* 自定义弹窗整体宽度 */
.custom-alert-box {
  width: 800px !important; /* 调整弹窗宽度 */
  max-width: 90vw; /* 限制最大宽度 */
}
</style>
