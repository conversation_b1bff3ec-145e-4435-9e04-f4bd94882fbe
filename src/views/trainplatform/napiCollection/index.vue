<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="患者ID" prop="patientUserId" >
        <el-input
          v-model="queryParams.patientUserId"
          placeholder="患者ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item> <el-form-item label="gms病历ID" prop="gmsId"  label-width="88px">
        <el-input
          v-model="queryParams.gmsId"
          placeholder="请输入gms病历ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="就诊号码" prop="jzhm">
        <el-input
          v-model="queryParams.jzhm"
          placeholder="请输入就诊号码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="住院号" prop="zyh">
        <el-input
          v-model="queryParams.zyh"
          placeholder="请输入住院号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="采集类型" prop="collectionType">
        <el-input
          v-model="queryParams.collectionType"
          placeholder="请输入采集类型"
          clearable
          @keyup.enter="handleQuery" style="width: 180px"
        />
      </el-form-item>
      <el-form-item label="测量日期" prop="collectionDate">
        <el-date-picker clearable style="width: 173px"
          v-model="queryParams.collectionDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择测量日期" >
        </el-date-picker>
      </el-form-item>
  
  
      <el-form-item label="评估类型" prop="assessmentType" >
        <el-select v-model="queryParams.assessmentType" placeholder="请选择评估类型" clearable style="width: 173px">
          <el-option
            v-for="dict in aimp_assessment_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="AI评估结果" prop="aiAssessmentResult"  label-width="88px">
        <el-select v-model="queryParams.aiAssessmentResult" placeholder="请选择AI评估结果" clearable style="width: 173px">
          <el-option
            v-for="dict in assessment_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['trainplatform:napiCollection:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['trainplatform:napiCollection:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['trainplatform:napiCollection:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['trainplatform:napiCollection:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="napiCollectionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="40" align="center" />
      <el-table-column label="id" align="center"  width="55" prop="id" />
      <el-table-column label="患者id" align="center" prop="patientUserId" width="70" />
      <el-table-column label="就诊号码" align="center" prop="jzhm" />
      <el-table-column label="住院号" align="center" prop="zyh"  width="70"/>
      <el-table-column label="gms系统病历ID" align="center" prop="gmsId" />
      <!-- <el-table-column label="对应的用户推理ID" align="center" prop="userInferenceId" /> -->
      <el-table-column label="采集类型" align="center" prop="collectionType" />
      <el-table-column label="测量日期" align="center" prop="collectionDate" width="100">
        <template #default="scope">
          <span>{{ parseTime(scope.row.collectionDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="纠正胎龄" align="center" prop="correctAge"  width="50"/>
     
      <el-table-column label="视频质量" align="center" prop="videoQuality">
        <template #default="scope">
          <dict-tag :options="aimp_video_quality" :value="scope.row.videoQuality"/>
        </template>
      </el-table-column>
      <el-table-column label="评估类型" align="center" prop="assessmentType">
        <template #default="scope">
          <dict-tag :options="aimp_assessment_type" :value="scope.row.assessmentType"/>
        </template>
      </el-table-column>
      <el-table-column label="AI评估结果" align="center" prop="aiAssessmentResult">
        <template #default="scope">
          <dict-tag :options="assessment_type" :value="scope.row.aiAssessmentResult"/>
        </template>
      </el-table-column>
      <el-table-column label="医生评估结果-阶段一" align="center" prop="doctorAssessmentStep1">
        <template #default="scope">
          <dict-tag :options="doctor_assessment_1_type" :value="scope.row.doctorAssessmentStep1"/>
        </template>
      </el-table-column>
      <el-table-column label="医生评估结果-阶段二" align="center" prop="doctorAssessmentStep2">
        <template #default="scope">
          <dict-tag :options="assessment_type" :value="scope.row.doctorAssessmentStep2"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width"  width="200">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['trainplatform:napiCollection:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['trainplatform:napiCollection:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改动态视频采集记录对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="napiCollectionRef" :model="form" :rules="rules" label-width="150px" inline>
        <el-form-item label="gms系统病历ID" prop="gmsId">
          <el-input v-model="form.gmsId" placeholder="请输入gms系统病历ID" />
        </el-form-item>
        <el-form-item label="就诊号码" prop="jzhm">
          <el-input v-model="form.jzhm" placeholder="请输入就诊号码" />
        </el-form-item>
        <el-form-item label="住院号" prop="zyh">
          <el-input v-model="form.zyh" placeholder="请输入住院号" />
        </el-form-item>
        <el-form-item label="对应的用户推理ID" prop="userInferenceId">
          <el-input v-model="form.userInferenceId" placeholder="请输入对应的用户推理ID" />
        </el-form-item>
        <el-form-item label="采集类型" prop="collectionType">
          <el-input v-model="form.collectionType" placeholder="请输入采集类型" />
        </el-form-item>
        <el-form-item label="测量日期" prop="collectionDate">
          <el-date-picker clearable
            v-model="form.collectionDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择测量日期"
            style="width: 173px">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="纠正胎龄" prop="correctAge"  type="number">
          <el-input v-model="form.correctAge" placeholder="请输入纠正胎龄" type/>
        </el-form-item>
        <!-- <el-form-item label="视频地址" prop="videoUrl">
          <el-input v-model="form.videoUrl" placeholder="请输入视频地址" />
        </el-form-item> -->
        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="form.deviceName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备型号" prop="deviceModel">
          <el-input v-model="form.deviceModel" placeholder="请输入设备型号" />
        </el-form-item>
        <el-form-item label="体重(kg)" prop="weight">
          <el-input v-model="form.weight" placeholder="请输入体重(kg)" />
        </el-form-item>
        <el-form-item label="身长(cm)" prop="height">
          <el-input v-model="form.height" placeholder="请输入身长(cm)" />
        </el-form-item>
        <el-form-item label="体温" prop="bodyTemperature">
          <el-input v-model="form.bodyTemperature" placeholder="请输入体温" />
        </el-form-item>
        <el-form-item label="心率" prop="hr">
          <el-input v-model="form.hr" placeholder="请输入心率" />
        </el-form-item>
        <el-form-item label="呼吸频率" prop="rr">
          <el-input v-model="form.rr" placeholder="请输入呼吸频率" />
        </el-form-item>
        <el-form-item label="舒张压" prop="diastolicPressure">
          <el-input v-model="form.diastolicPressure" placeholder="请输入舒张压" />
        </el-form-item>
        <el-form-item label="收缩压" prop="systolicPressure">
          <el-input v-model="form.systolicPressure" placeholder="请输入收缩压" />
        </el-form-item>
        <el-form-item label="视频质量" prop="videoQuality">
          <el-radio-group v-model="form.videoQuality">
            <el-radio
              v-for="dict in aimp_video_quality"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="评估类型" prop="assessmentType">
          <el-radio-group v-model="form.assessmentType">
            <el-radio
              v-for="dict in aimp_assessment_type"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="AI评估结果" prop="aiAssessmentResult">
          <el-radio-group v-model="form.aiAssessmentResult">
            <el-radio
              v-for="dict in assessment_type"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="医生评估结果-阶段一" prop="doctorAssessmentStep1">
          <el-radio-group v-model="form.doctorAssessmentStep1">
            <el-radio
              v-for="dict in doctor_assessment_1_type"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="医生评估结果-阶段二" prop="doctorAssessmentStep2">
          <el-radio-group v-model="form.doctorAssessmentStep2">
            <el-radio
              v-for="dict in assessment_type"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="医生评估结果-其他" prop="doctorAssessmentOthers">
          <el-radio-group v-model="form.doctorAssessmentOthers">
            <el-radio
              v-for="dict in doctor_assessment_others"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        
        
        
        <el-row style="width: 100%">
          <el-col :span="24">
            <el-form-item label="预约下次评估时间" prop="nextAssessmentTime">
              <el-date-picker clearable
                v-model="form.nextAssessmentTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择预约下次评估时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="width: 100%">
          <el-col :span="24">
            <el-form-item label="早期干预建议" prop="suggestion">
                <el-input v-model="form.suggestion" type="textarea" placeholder="请输入内容" 
                style="width: 525px"/>
              </el-form-item>
          </el-col>
        </el-row>
       
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" 
          style="width: 525px"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="NapiCollection">
import { listNapiCollection, getNapiCollection, delNapiCollection, addNapiCollection, updateNapiCollection } from "@/api/trainplatform/napiCollection";

const { proxy } = getCurrentInstance();
const { aimp_video_quality, doctor_assessment_others, doctor_assessment_1_type, assessment_type, aimp_assessment_type } = proxy.useDict('aimp_video_quality', 'doctor_assessment_others', 'doctor_assessment_1_type', 'assessment_type', 'aimp_assessment_type');

const napiCollectionList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    jzhm: null,
    zyh: null,
    patientUserId: null,
    gmsId: null,
    userInferenceId: null,
    collectionType: null,
    collectionDate: null,
    correctAge: null,
    videoUrl: null,
    deviceName: null,
    deviceModel: null,
    weight: null,
    height: null,
    bodyTemperature: null,
    hr: null,
    rr: null,
    diastolicPressure: null,
    systolicPressure: null,
    videoQuality: null,
    assessmentType: null,
    aiAssessmentResult: null,
    doctorUserId: null,
    doctorAssessmentStep1: null,
    doctorAssessmentStep2: null,
    doctorAssessmentOthers: null,
    suggestion: null,
    nextAssessmentTime: null,
    createdTime: null,
    updatedTime: null,
    createUser: null,
    updateUser: null,
    collectionUserName: null
  },
  rules: {
    gmsId: [
      { required: true, message: "gms系统病历ID不能为空", trigger: "blur" }
    ],
    userInferenceId: [
      { pattern: /^\d+$/, message: "用户推理ID只能输入数字", trigger: "blur" }
    ],
    zyh: [
      { pattern: /^\d+$/, message: "住院号只能输入数字", trigger: "blur" }
    ],
    correctAge: [
      { pattern: /^\d+$/, message: "纠正胎龄只能输入数字", trigger: "blur" }
    ],
    weight: [
      { pattern: /^\d+(\.\d{1,2})?$/, message: "请输入数字，最多 2 位小数", trigger: "blur" }
    ],
    height: [
      { pattern: /^\d+(\.\d{1,2})?$/, message: "请输入数字，最多 2 位小数", trigger: "blur" } 
    ],
    correctAge: [
      { pattern: /^\d+$/, message: "体重只能输入数字", trigger: "blur" }
    ],
    bodyTemperature: [
      { pattern: /^\d+(\.\d{1,2})?$/, message: "请输入数字，最多 2 位小数", trigger: "blur" }
    ],
    hr: [
      { pattern: /^\d+$/, message: "心率只能输入数字", trigger: "blur" }
    ],
    rr: [
      { pattern: /^\d+$/, message: "呼吸频率只能输入数字", trigger: "blur" }
    ],
    diastolicPressure: [
      { pattern: /^\d+$/, message: "舒张压只能输入数字", trigger: "blur" }
    ],
    systolicPressure: [
      { pattern: /^\d+$/, message: "收缩压只能输入数字", trigger: "blur" }
    ],
    
    jzhm: [
      { max: 16, message: "最多输入 16 个字符", trigger: "blur" }
    ],
    gmsId: [
      { max: 32, message: "最多输入 32 个字符", trigger: "blur" }
    ],
    collectionType: [
      { max: 128, message: "最多输入 128 个字符", trigger: "blur" }
    ],
    videoUrl: [
      { max: 128, message: "最多输入 128 个字符", trigger: "blur" }
    ],
    deviceName: [
      { max: 128, message: "最多输入 128 个字符", trigger: "blur" }
    ],
    deviceModel: [
      { max: 128, message: "最多输入 128 个字符", trigger: "blur" }
    ],
    remark: [
      { max: 128, message: "最多输入 128 个字符", trigger: "blur" }
    ],
    collectionUserName: [
      { max: 20, message: "最多输入 20 个字符", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);


/** 查询动态视频采集记录列表 */
function getList() {
  loading.value = true;
  listNapiCollection(queryParams.value).then(response => {
    napiCollectionList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    patientUserId: null,
    jzhm: null,
    zyh: null,
    gmsId: null,
    userInferenceId: null,
    collectionType: null,
    collectionDate: null,
    correctAge: null,
    videoUrl: null,
    deviceName: null,
    deviceModel: null,
    weight: null,
    height: null,
    bodyTemperature: null,
    hr: null,
    rr: null,
    diastolicPressure: null,
    systolicPressure: null,
    videoQuality: null,
    assessmentType: null,
    aiAssessmentResult: null,
    doctorUserId: null,
    doctorAssessmentStep1: null,
    doctorAssessmentStep2: null,
    doctorAssessmentOthers: null,
    suggestion: null,
    nextAssessmentTime: null,
    createTime: null,
    updateTime: null,
    createBy: null,
    updateBy: null,
    remark: null
  };
  proxy.resetForm("napiCollectionRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加动态视频采集记录";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getNapiCollection(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改动态视频采集记录";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["napiCollectionRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateNapiCollection(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addNapiCollection(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除动态视频采集记录编号为"' + _ids + '"的数据项？').then(function() {
    return delNapiCollection(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('trainplatform/napiCollection/export', {
    ...queryParams.value
  }, `napiCollection_${new Date().getTime()}.xlsx`)
}

getList();
</script>
