<template>
  <div class="app-container">
    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编号" align="center" prop="id" />
      <el-table-column label="账号ID" align="center" prop="userId" />
      <el-table-column label="人员姓名" align="center" prop="userName" />
      <el-table-column label="角色" align="center" prop="roleName" width="180" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['trainplatform:user:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改课题参与人员对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="userRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="课题编号" prop="projectNo">
          <el-input v-model="form.projectNo" readonly placeholder="请输入课题编号" />
        </el-form-item>
        <el-form-item label="选择用户" prop="userIds">
          <el-popover placement="bottom-start" :width="840" trigger="click">
            <template #reference>
              <el-input v-model="form.userIds" placeholder="请选择用户" readonly />
            </template>
            <custom-person-selector @selected="selected" />
          </el-popover>
        </el-form-item>

        <el-form-item label="选择角色" prop="roleId">
          <el-select v-model="form.roleId" class="m-2" placeholder="Select">
            <el-option v-for="role in roleList" :key="role.value" :label="role.dictLabel" :value="role.dictValue" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="UserList">
import { listUser, getRoleByProject, delUser, batchAdd, updateUser } from '@/api/trainplatform/user'
import { deptTreeSelect } from '@/api/system/user'

import { ref } from 'vue'
import CustomPersonSelector from '@/components/SelectOrg'

const route = useRoute()
const userList = ref([])
const roleList = ref([])
const open = ref(false)
const loading = ref(true)
const roleLoading = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const roleTotal = ref(0)
const title = ref('')
const deptOptions = ref(undefined)

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectNo: route.params && route.params.projectId,
    userId: null,
    createBy: null
  },
  rules: {
    projectNo: [{ required: true, message: '课题编号不能为空', trigger: 'blur' }],
    userIds: [{ required: true, message: '请选择用户', trigger: 'blur' }],
    roleId: [{ required: true, message: '请选择课程', trigger: 'blur' }]
  }
})

const { proxy } = getCurrentInstance()
const { queryParams, form, rules } = toRefs(data)

/** 查询课题参与人员列表 */
function getList() {
  loading.value = true
  listUser(queryParams.value).then((response) => {
    userList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

function getDeptTree() {
  deptTreeSelect().then((response) => {
    deptOptions.value = response.data
  })
}

function selected(ids) {
  if (ids && ids.value) {
    form.value.userIds = ids.value
  }
}

/** 查询课题参与人员列表 */
function queryRoleByProject() {
  roleLoading.value = true
  getRoleByProject(queryParams.value).then((response) => {
    roleList.value = response.data
    roleTotal.value = response.total
    roleLoading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    projectNo: route.params && route.params.projectId,
    userIds: null,
    roleId: null
  }
  proxy.resetForm('userRef')
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = '添加参与人员'
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['userRef'].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateUser(form.value).then((response) => {
          proxy.$modal.msgSuccess('修改成功')
          open.value = false
          getList()
        })
      } else {
        batchAdd(form.value).then((response) => {
          proxy.$modal.msgSuccess('新增成功')
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal
    .confirm('是否确认删除课题参与人员编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delUser(_ids)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess('删除成功')
    })
    .catch(() => {})
}

getList()
getDeptTree()
queryRoleByProject()

defineExpose({ handleAdd })
</script>
