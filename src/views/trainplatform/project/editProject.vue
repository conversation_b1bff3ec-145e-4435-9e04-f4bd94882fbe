<template>
  <el-card>
    <el-tabs v-model="activeName">
      <el-tab-pane label="课程参与人员" name="basic">
        <div><userList ref="UserList"></userList></div>
      </el-tab-pane>
      <el-tab-pane label="课程模型库" name="columnInfo"> </el-tab-pane>
      <el-tab-pane label="课程数据库" name="genInfo"> </el-tab-pane>
    </el-tabs>
    <div class="adduser clearfix">
      <el-button color="#000000" @click="handleAdd" v-hasPermi="['trainplatform:user:add']"> 添加人员 </el-button>
      <el-button color="#000000" @click="handleAdd" v-hasPermi="['trainplatform:user:add']"> 邀请人员 </el-button>
      <el-button @click="close()">返回</el-button>
    </div>
  </el-card>
</template>

<script setup name="editProject">
import userList from './userList.vue'
const route = useRoute()
import router from '@/router'
const { proxy } = getCurrentInstance()

const activeName = ref('basic')

const UserList = ref(null)
const handleAdd = () => {
  if (UserList.value) {
    UserList.value.handleAdd()
  }
}

function close() {
  const obj = router.replace({ path: '/trainplatform/project' })
}

;(() => {
  const projectNo = route.params && route.params.projectId
  if (projectNo) {
  }
})()
</script>

<style lang="css" scoped>
.adduser {
  position: absolute;
  top: 18px;
  right: 20px;
}
</style>
