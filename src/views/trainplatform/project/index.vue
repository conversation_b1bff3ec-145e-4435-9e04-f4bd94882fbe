<template>
  <div class="app-container">
    <el-row class="card" :gutter="12">
      <el-card @click="handleAdd" class="custom-card">
        <div class="add-item">
          <el-icon :size="80" color="#cccccc">
            <Plus />
          </el-icon>
        </div>
      </el-card>
      <el-card v-for="project in projectList" :key="project" class="text item custom-card" @click="handleEditProject(project)">
        <template #header>
          <el-row>
            <el-col :span="12">
              <div class="title">
                <span>{{ project.projectName }}</span>
              </div>
            </el-col>
            <el-col class="num" :span="12">
              <span>人数：{{ project.userNumber }}</span>
            </el-col>
          </el-row>
        </template>
        <p class="text item content">{{ project.projectDescription }}</p>
        <el-row>
          <el-col :span="18">
            <div class="time">{{ $dayjs(project.createTime).format('YYYY-MM-DD HH:mm') }}</div>
          </el-col>
          <el-col :span="6">
            <el-icon class="button" :size="32" color="#666666" @click.stop="handleDelete(project)">
              <Delete />
            </el-icon>
          </el-col>
        </el-row>
      </el-card>
    </el-row>

    <!-- 添加或修改课题对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="500px"
      append-to-body
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="projectRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="课题编号" prop="projectNo">
          <el-input v-model="form.projectNo" placeholder="请输入课题编号" />
        </el-form-item>
        <el-form-item label="课题名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入课题名称" />
        </el-form-item>
        <el-form-item label="课题描述" prop="projectDescription">
          <el-input v-model="form.projectDescription" placeholder="请输入课题描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Project">
import { listProject, getProject, delProject, addProject, updateProject } from '@/api/trainplatform/project'

const { proxy } = getCurrentInstance()

const projectList = ref([])
const open = ref(false)
const loading = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref('')

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectNo: null,
    projectName: null,
    projectStatus: null,
    projectDescription: null,
    createdTime: null,
    updatedTime: null,
    createBy: null,
    updateBy: null
  },
  rules: {
    projectNo: [{ required: true, message: '课题编号不能为空', trigger: 'blur' }],
    projectName: [{ required: true, message: '课题名称不能为空', trigger: 'blur' }]
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询课题列表 */
function getList() {
  loading.value = true
  listProject(queryParams.value).then((response) => {
    projectList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    projectNo: null,
    projectName: null,
    projectStatus: null,
    projectDescription: null,
    createdTime: null,
    updatedTime: null,
    createBy: null,
    updateBy: null
  }
  proxy.resetForm('projectRef')
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef')
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = '添加课题'
}

/** 修改按钮操作 */
function handleEditProject(project) {
  const obj = { path: '/trainplatform/project-edit/index/' + project.projectNo }
  proxy.$tab.closeOpenPage(obj)
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getProject(_id).then((response) => {
    form.value = response.data
    open.value = true
    title.value = '修改课题'
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['projectRef'].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateProject(form.value).then((response) => {
          proxy.$modal.msgSuccess('修改成功')
          open.value = false
          getList()
        })
      } else {
        addProject(form.value).then((response) => {
          proxy.$modal.msgSuccess('新增成功')
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal
    .confirm('是否确认删除课题编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delProject(_ids)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess('删除成功')
    })
    .catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'trainplatform/project/export',
    {
      ...queryParams.value
    },
    `project_${new Date().getTime()}.xlsx`
  )
}

getList()
</script>