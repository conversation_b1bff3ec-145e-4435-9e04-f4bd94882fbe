<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户id" prop="patientUserId">
        <el-input
          v-model="queryParams.patientUserId"
          placeholder="请输入用户id"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文件存储key" prop="fileKey">
        <el-input
          v-model="queryParams.fileKey"
          placeholder="请输入文件存储key"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文件来源" prop="source">
        <el-select v-model="queryParams.source" placeholder="请选择文件来源" clearable>
          <el-option
            v-for="dict in file_source_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="业务场景主键ID" prop="bizId">
        <el-input
          v-model="queryParams.bizId"
          placeholder="请输入业务场景主键ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文件名称" prop="fileName">
        <el-input
          v-model="queryParams.fileName"
          placeholder="请输入文件名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文件格式" prop="fileFormat">
        <el-input
          v-model="queryParams.fileFormat"
          placeholder="请输入文件格式"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文件大小(b)" prop="fileSize">
        <el-input
          v-model="queryParams.fileSize"
          placeholder="请输入文件大小(b)"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建日期" prop="createdTime">
        <el-date-picker clearable
          v-model="queryParams.createdTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择创建日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['trainplatform:files:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['trainplatform:files:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['trainplatform:files:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['trainplatform:files:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="filesList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="用户id" align="center" prop="patientUserId" />
      <el-table-column label="文件存储key" align="center" prop="fileKey" />
      <el-table-column label="文件来源" align="center" prop="source">
        <template #default="scope">
          <dict-tag :options="file_source_type" :value="scope.row.source"/>
        </template>
      </el-table-column>
      <el-table-column label="业务场景主键ID" align="center" prop="bizId" />
      <el-table-column label="文件名称" align="center" prop="fileName" />
      <el-table-column label="文件格式" align="center" prop="fileFormat" />
      <el-table-column label="文件大小(b)" align="center" prop="fileSize" />
      <el-table-column label="创建日期" align="center" prop="createdTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['trainplatform:files:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['trainplatform:files:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改患者文件资料存储对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="filesRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户id" prop="patientUserId">
          <el-input v-model="form.patientUserId" placeholder="请输入用户id" />
        </el-form-item>
        <el-form-item label="文件存储key" prop="fileKey">
          <el-input v-model="form.fileKey" placeholder="请输入文件存储key" />
        </el-form-item>
        <el-form-item label="文件来源" prop="source">
          <el-select v-model="form.source" placeholder="请选择文件来源">
            <el-option
              v-for="dict in file_source_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="业务场景主键ID" prop="bizId">
          <el-input v-model="form.bizId" placeholder="请输入业务场景主键ID" />
        </el-form-item>
        <el-form-item label="文件名称" prop="fileName">
          <el-input v-model="form.fileName" placeholder="请输入文件名称" />
        </el-form-item>
        <el-form-item label="文件格式" prop="fileFormat">
          <el-input v-model="form.fileFormat" placeholder="请输入文件格式" />
        </el-form-item>
        <el-form-item label="文件大小(b)" prop="fileSize">
          <el-input v-model="form.fileSize" placeholder="请输入文件大小(b)" />
        </el-form-item>
        <el-form-item label="创建日期" prop="createdTime">
          <el-date-picker clearable
            v-model="form.createdTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择创建日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Files">
import { listFiles, getFiles, delFiles, addFiles, updateFiles } from "@/api/trainplatform/files";

const { proxy } = getCurrentInstance();
const { file_source_type } = proxy.useDict('file_source_type');

const filesList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    patientUserId: null,
    fileKey: null,
    source: null,
    bizId: null,
    fileName: null,
    fileFormat: null,
    fileSize: null,
    createdTime: null
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询患者文件资料存储列表 */
function getList() {
  loading.value = true;
  listFiles(queryParams.value).then(response => {
    filesList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    patientUserId: null,
    fileKey: null,
    source: null,
    bizId: null,
    fileName: null,
    fileFormat: null,
    fileSize: null,
    createdTime: null
  };
  proxy.resetForm("filesRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加患者文件资料存储";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getFiles(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改患者文件资料存储";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["filesRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateFiles(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addFiles(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除患者文件资料存储编号为"' + _ids + '"的数据项？').then(function() {
    return delFiles(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('trainplatform/files/export', {
    ...queryParams.value
  }, `files_${new Date().getTime()}.xlsx`)
}

getList();
</script>
