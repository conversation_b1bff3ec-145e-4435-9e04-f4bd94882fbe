<template>
  <div class="app-container home">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <div class="banner-text">
          <h1 class="banner-title">
            <el-icon class="title-icon"><DataAnalysis /></el-icon>
            医疗大数据人工智能训练平台
          </h1>
          <p class="banner-subtitle">
            专业的AI模型管理与训练平台，助力医疗数据智能化分析
          </p>
        </div>
        <div class="banner-stats">
          <div class="stat-item">
            <div class="stat-number">{{ stats.totalModels }}</div>
            <div class="stat-label">训练模型</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ stats.activeProjects }}</div>
            <div class="stat-label">活跃项目</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ stats.dataSize }}</div>
            <div class="stat-label">数据量(GB)</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作卡片 -->
    <div class="quick-actions">
      <h2 class="section-title">
        <el-icon><Operation /></el-icon>
        快速操作
      </h2>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6" v-for="action in quickActions" :key="action.id">
          <el-card class="action-card" shadow="hover" @click="handleQuickAction(action.route)">
            <div class="action-content">
              <div class="action-icon" :style="{ backgroundColor: action.color }">
                <el-icon :size="24">
                  <component :is="action.icon" />
                </el-icon>
              </div>
              <div class="action-info">
                <h3>{{ action.title }}</h3>
                <p>{{ action.description }}</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据概览 -->
    <div class="data-overview">
      <h2 class="section-title">
        <el-icon><TrendCharts /></el-icon>
        数据概览
      </h2>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <el-card class="overview-card">
            <div class="card-header">
              <span>模型训练状态</span>
              <el-icon class="header-icon"><Monitor /></el-icon>
            </div>
            <div class="progress-list">
              <div class="progress-item" v-for="item in trainingProgress" :key="item.name">
                <div class="progress-info">
                  <span class="progress-name">{{ item.name }}</span>
                  <span class="progress-percent">{{ item.progress }}%</span>
                </div>
                <el-progress :percentage="item.progress" :color="item.color" :show-text="false" />
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="8">
          <el-card class="overview-card">
            <div class="card-header">
              <span>系统资源</span>
              <el-icon class="header-icon"><Cpu /></el-icon>
            </div>
            <div class="resource-list">
              <div class="resource-item" v-for="resource in systemResources" :key="resource.name">
                <div class="resource-info">
                  <span class="resource-name">{{ resource.name }}</span>
                  <span class="resource-value">{{ resource.value }}</span>
                </div>
                <div class="resource-bar">
                  <div class="resource-fill" :style="{ width: resource.percentage + '%', backgroundColor: resource.color }"></div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="24" :md="8">
          <el-card class="overview-card">
            <div class="card-header">
              <span>最近活动</span>
              <el-icon class="header-icon"><Clock /></el-icon>
            </div>
            <div class="activity-list">
              <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
                <div class="activity-icon" :style="{ backgroundColor: activity.color }">
                  <el-icon :size="16">
                    <component :is="activity.icon" />
                  </el-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-time">{{ activity.time }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 功能模块导航 -->
    <div class="feature-modules">
      <h2 class="section-title">
        <el-icon><Grid /></el-icon>
        功能模块
      </h2>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" v-for="module in featureModules" :key="module.id">
          <el-card class="module-card" shadow="hover" @click="handleModuleClick(module.route)">
            <div class="module-content">
              <div class="module-header">
                <div class="module-icon" :style="{ backgroundColor: module.color }">
                  <el-icon :size="32">
                    <component :is="module.icon" />
                  </el-icon>
                </div>
                <h3 class="module-title">{{ module.title }}</h3>
              </div>
              <p class="module-description">{{ module.description }}</p>
              <div class="module-stats">
                <span class="module-count">{{ module.count }} 项</span>
                <el-icon class="arrow-icon"><ArrowRight /></el-icon>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup name="Index">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  DataAnalysis,
  Operation,
  TrendCharts,
  Monitor,
  Cpu,
  Clock,
  Grid,
  ArrowRight,
  Plus,
  View,
  Setting,
  Document,
  Files,
  User,
  Notification,
  DataBoard,
  Connection
} from '@element-plus/icons-vue'

const router = useRouter()

// 统计数据
const stats = ref({
  totalModels: 156,
  activeProjects: 23,
  dataSize: 2048
})

// 快速操作
const quickActions = ref([
  {
    id: 1,
    title: '新建项目',
    description: '创建新的AI训练项目',
    icon: 'Plus',
    color: '#409EFF',
    route: '/trainplatform/project'
  },
  {
    id: 2,
    title: '数据管理',
    description: '管理训练数据集',
    icon: 'Files',
    color: '#67C23A',
    route: '/trainplatform/files'
  },
  {
    id: 3,
    title: '模型检测',
    description: '查看模型检测结果',
    icon: 'View',
    color: '#E6A23C',
    route: '/trainplatform/detection'
  },
  {
    id: 4,
    title: '系统设置',
    description: '配置系统参数',
    icon: 'Setting',
    color: '#F56C6C',
    route: '/system'
  }
])

// 训练进度
const trainingProgress = ref([
  { name: '肺部CT识别模型', progress: 85, color: '#409EFF' },
  { name: '心电图分析模型', progress: 62, color: '#67C23A' },
  { name: '病理图像分类', progress: 94, color: '#E6A23C' },
  { name: '医学影像分割', progress: 38, color: '#F56C6C' }
])

// 系统资源
const systemResources = ref([
  { name: 'CPU使用率', value: '68%', percentage: 68, color: '#409EFF' },
  { name: 'GPU使用率', value: '82%', percentage: 82, color: '#67C23A' },
  { name: '内存使用', value: '45%', percentage: 45, color: '#E6A23C' },
  { name: '存储空间', value: '73%', percentage: 73, color: '#F56C6C' }
])

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '肺部CT模型训练完成',
    time: '2小时前',
    icon: 'DataBoard',
    color: '#67C23A'
  },
  {
    id: 2,
    title: '新增训练数据集',
    time: '4小时前',
    icon: 'Document',
    color: '#409EFF'
  },
  {
    id: 3,
    title: '用户权限更新',
    time: '6小时前',
    icon: 'User',
    color: '#E6A23C'
  },
  {
    id: 4,
    title: '系统维护通知',
    time: '1天前',
    icon: 'Notification',
    color: '#F56C6C'
  }
])

// 功能模块
const featureModules = ref([
  {
    id: 1,
    title: '训练平台',
    description: 'AI模型训练与管理',
    icon: 'DataAnalysis',
    color: '#409EFF',
    count: 23,
    route: '/trainplatform'
  },
  {
    id: 2,
    title: '系统管理',
    description: '用户权限与系统配置',
    icon: 'Setting',
    color: '#67C23A',
    count: 15,
    route: '/system'
  },
  {
    id: 3,
    title: '监控中心',
    description: '系统运行状态监控',
    icon: 'Monitor',
    color: '#E6A23C',
    count: 8,
    route: '/monitor'
  }
])

// 处理快速操作点击
const handleQuickAction = (route) => {
  if (route) {
    router.push(route)
  }
}

// 处理模块点击
const handleModuleClick = (route) => {
  if (route) {
    router.push(route)
  }
}

onMounted(() => {
  // 可以在这里加载实际数据
  console.log('首页加载完成')
})
</script>

<style scoped lang="scss">
.home {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 84px);

  .welcome-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 40px;
    margin-bottom: 30px;
    color: white;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
    }

    .banner-content {
      position: relative;
      z-index: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 30px;
    }

    .banner-text {
      flex: 1;
      min-width: 300px;

      .banner-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0 0 15px 0;
        display: flex;
        align-items: center;
        gap: 15px;

        .title-icon {
          font-size: 3rem;
          color: #ffd700;
        }
      }

      .banner-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin: 0;
        line-height: 1.6;
      }
    }

    .banner-stats {
      display: flex;
      gap: 40px;
      flex-wrap: wrap;

      .stat-item {
        text-align: center;

        .stat-number {
          font-size: 2.5rem;
          font-weight: 700;
          color: #ffd700;
          display: block;
        }

        .stat-label {
          font-size: 0.9rem;
          opacity: 0.8;
          margin-top: 5px;
        }
      }
    }
  }

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 10px;

    .el-icon {
      color: #409EFF;
    }
  }

  .quick-actions {
    margin-bottom: 30px;

    .action-card {
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;
      border-radius: 12px;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      }

      .action-content {
        display: flex;
        align-items: center;
        gap: 15px;

        .action-icon {
          width: 50px;
          height: 50px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          flex-shrink: 0;
        }

        .action-info {
          h3 {
            margin: 0 0 5px 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
          }

          p {
            margin: 0;
            font-size: 0.9rem;
            color: #7f8c8d;
            line-height: 1.4;
          }
        }
      }
    }
  }

  .data-overview {
    margin-bottom: 30px;

    .overview-card {
      height: 100%;
      border-radius: 12px;
      border: none;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        font-weight: 600;
        color: #2c3e50;

        .header-icon {
          color: #409EFF;
          font-size: 1.2rem;
        }
      }

      .progress-list {
        .progress-item {
          margin-bottom: 15px;

          &:last-child {
            margin-bottom: 0;
          }

          .progress-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;

            .progress-name {
              font-size: 0.9rem;
              color: #2c3e50;
            }

            .progress-percent {
              font-size: 0.9rem;
              font-weight: 600;
              color: #409EFF;
            }
          }
        }
      }

      .resource-list {
        .resource-item {
          margin-bottom: 15px;

          &:last-child {
            margin-bottom: 0;
          }

          .resource-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;

            .resource-name {
              font-size: 0.9rem;
              color: #2c3e50;
            }

            .resource-value {
              font-size: 0.9rem;
              font-weight: 600;
            }
          }

          .resource-bar {
            height: 6px;
            background: #f0f2f5;
            border-radius: 3px;
            overflow: hidden;

            .resource-fill {
              height: 100%;
              border-radius: 3px;
              transition: width 0.3s ease;
            }
          }
        }
      }

      .activity-list {
        .activity-item {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 15px;

          &:last-child {
            margin-bottom: 0;
          }

          .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
          }

          .activity-content {
            flex: 1;

            .activity-title {
              font-size: 0.9rem;
              color: #2c3e50;
              margin-bottom: 2px;
            }

            .activity-time {
              font-size: 0.8rem;
              color: #7f8c8d;
            }
          }
        }
      }
    }
  }

  .feature-modules {
    .module-card {
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;
      border-radius: 12px;
      height: 100%;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
      }

      .module-content {
        .module-header {
          display: flex;
          align-items: center;
          gap: 15px;
          margin-bottom: 15px;

          .module-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
          }

          .module-title {
            margin: 0;
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
          }
        }

        .module-description {
          margin: 0 0 20px 0;
          color: #7f8c8d;
          line-height: 1.5;
        }

        .module-stats {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .module-count {
            font-size: 0.9rem;
            color: #409EFF;
            font-weight: 600;
          }

          .arrow-icon {
            color: #c0c4cc;
            transition: all 0.3s ease;
          }
        }

        &:hover .arrow-icon {
          color: #409EFF;
          transform: translateX(3px);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .home {
    padding: 15px;

    .welcome-banner {
      padding: 30px 20px;

      .banner-content {
        flex-direction: column;
        text-align: center;
      }

      .banner-text .banner-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 10px;
      }

      .banner-stats {
        justify-content: center;
        gap: 30px;
      }
    }

    .section-title {
      font-size: 1.3rem;
    }
  }
}

@media (max-width: 480px) {
  .home {
    .welcome-banner {
      .banner-text .banner-title {
        font-size: 1.8rem;
      }

      .banner-stats {
        gap: 20px;

        .stat-item .stat-number {
          font-size: 2rem;
        }
      }
    }
  }
}
</style>
