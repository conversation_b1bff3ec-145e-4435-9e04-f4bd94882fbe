<template>
  <div class="app-container">
    <el-row :gutter="20">
      <splitpanes class="default-theme">
        <!--部门数据-->
        <pane size="30">
          <el-col>
            <div class="head-container">
              <el-tree
                :data="deptOptions"
                :props="{ label: 'label', children: 'children' }"
                :expand-on-click-node="false"
                ref="deptTreeRef"
                node-key="id"
                highlight-current
                default-expand-all
                @node-click="handleNodeClick"
              />
            </div>
          </el-col>
        </pane>
        <!--用户数据-->
        <pane size="70">
          <el-col>
            <el-form-item label="" prop="userName">
              <el-input v-model="queryParams.userName" placeholder="请输入用户名称" @keyup.enter="handleQuery">
                <template #append>
                  <el-button :icon="Search" @click="handleQuery" />
                </template>
              </el-input>
            </el-form-item>

            <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="50" />
              <el-table-column label="用户编号" key="userId" prop="userId" />
              <el-table-column label="用户名称" key="userName" prop="userName" />
              <el-table-column label="手机号码" key="phonenumber" prop="phonenumber" />
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </el-col>
        </pane>
      </splitpanes>
    </el-row>
    <el-footer style="line-height: 80px; text-align: center">
      <el-button type="primary" @click="selectUser">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </el-footer>
  </div>
</template>

<script setup name="User">
import { Search } from '@element-plus/icons-vue'
import { listUser, deptTreeSelect } from '@/api/system/user'
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'

const { proxy } = getCurrentInstance()

const userList = ref([])
const loading = ref(true)
const ids = ref([])
const total = ref(0)
const deptOptions = ref(undefined)
const emit = defineEmits(['selected'])

const data = reactive({
  form: {},
  queryParams: {},
  rules: {}
})

const { queryParams } = toRefs(data)

function getList() {
  loading.value = true

  let search = queryParams.value
  search.params =
    typeof search.params === 'object' && search.params !== null && !Array.isArray(search.params) ? search.params : {}
  search.params['isContainsAdmin'] = 0

  listUser(queryParams.value).then((res) => {
    loading.value = false
    userList.value = res.rows
    total.value = res.total
  })
}

function getDeptTree() {
  deptTreeSelect().then((response) => {
    deptOptions.value = response.data
  })
}

function handleNodeClick(data) {
  queryParams.value.deptId = data.id
  handleQuery()
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.userId)
}

function selectUser() {
  emit('selected', ids)
  document.body.click()
}

/** 取消按钮 */
function cancel() {
  document.body.click()
}

getDeptTree()
getList()
</script>

<style lang="css" scoped>
.custom-cls {
  min-height: 600px; /* 设置最小高度 */
  max-height: 800px; /* 设置最大高度 */
  overflow-y: auto; /* 当内容超出最大高度时，显示滚动条 */
}
</style>
