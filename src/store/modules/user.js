import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { isHttp, isEmpty } from '@/utils/validate'
import defAva from '@/assets/images/profile.jpg'

const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken(),
    id: '',
    name: '',
    avatar: '',
    roles: [],
    permissions: []
  }),
  actions: {
    // 登录
    login(userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      const code = userInfo.code
      const uuid = userInfo.uuid

      return new Promise((resolve, reject) => {
        // 开发环境模拟登录
        if (import.meta.env.VITE_APP_ENV === 'development') {
          // 简单的模拟验证
          if (username === 'admin' && password === 'admin123') {
            // 模拟token
            const mockToken = 'mock-token-' + Date.now()
            setToken(mockToken)
            this.token = mockToken

            // 模拟用户信息
            this.id = '1'
            this.name = '管理员'
            this.avatar = defAva
            this.roles = ['admin']
            this.permissions = ['*:*:*']

            console.log('开发模式：模拟登录成功')
            resolve({ token: mockToken })
            return
          } else {
            reject(new Error('用户名或密码错误'))
            return
          }
        }

        // 生产环境调用真实API
        login(username, password, code, uuid)
          .then((res) => {
            setToken(res.token)
            this.token = res.token
            resolve(res)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    // 获取用户信息
    getInfo() {
      return new Promise((resolve, reject) => {
        // 开发环境返回模拟用户信息
        if (import.meta.env.VITE_APP_ENV === 'development' && this.token && this.token.startsWith('mock-token-')) {
          const mockUserInfo = {
            user: {
              userId: '1',
              userName: '管理员',
              avatar: ''
            },
            roles: ['admin'],
            permissions: ['*:*:*']
          }

          this.id = mockUserInfo.user.userId
          this.name = mockUserInfo.user.userName
          this.avatar = defAva
          this.roles = mockUserInfo.roles
          this.permissions = mockUserInfo.permissions

          resolve(mockUserInfo)
          return
        }

        // 生产环境调用真实API
        getInfo()
          .then((res) => {
            const user = res.user
            let avatar = user.avatar || ''
            if (!isHttp(avatar)) {
              avatar = isEmpty(avatar) ? defAva : import.meta.env.VITE_APP_BASE_API + avatar
            }
            if (res.roles && res.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              this.roles = res.roles
              this.permissions = res.permissions
            } else {
              this.roles = ['ROLE_DEFAULT']
            }
            this.id = user.userId
            this.name = user.userName
            this.avatar = avatar
            resolve(res)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    // 退出系统
    logOut() {
      return new Promise((resolve, reject) => {
        logout(this.token)
          .then(() => {
            this.token = ''
            this.roles = []
            this.permissions = []
            removeToken()
            resolve()
          })
          .catch((error) => {
            reject(error)
          })
      })
    }
  }
})

export default useUserStore
